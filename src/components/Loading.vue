<template>
    <div v-if="config.visible" class="loading-overlay">
        <div class="loading-content">
            <i v-if="config.status === 'loaded'" class="pi pi-check-circle"></i>
            <i v-if="config.status === 'failed'" class="pi pi-exclamation-circle"></i>
            <i v-if="config.status === 'loading'" class="pi pi-spin pi-spinner"></i>
            <p>{{ config.message }}</p>
        </div>
    </div>
</template>

<script setup>
const props = defineProps({
    // 配置对象，包含显示状态、加载状态和消息
    config: {
        type: Object,
        default: () => ({
            visible: false,
            status: 'loading',
            message: '加载中...'
        })
    }
});
</script>

<style lang="scss" scoped>
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: transparent;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: $system-blue;

    p {
        margin-top: 15px;
        font-weight: bold;
        color: $system-blue;
    }

    .pi {
        font-size: 1.25rem;
        color: $system-blue;
    }
}
</style>
