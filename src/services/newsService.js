/**
 * 新闻API服务 - 简化版
 * 获取AI相关资讯
 */

// 支持的平台配置
const PLATFORMS = {
    '36kr': { name: '36氪' },
    juejin: { name: '掘金' },
    zhihu: { name: '知乎' }
};

// AI相关关键词（精简版）
const CAR_KEYWORDS = [
    // === 基础车辆类型 ===
    '汽车',
    '新能源汽车',
    '电动汽车',
    '燃油车',
    '混动',
    '新车',
    '车型',
    '汽车品牌',
    '混合动力',
    '纯电动',
    '插电混动',
    '增程式',
    '氢燃料电池',
    'SUV',
    '轿车',
    '跑车',
    '皮卡',
    'MPV',
    '微型车',
    '小型车',
    '紧凑型车',
    '中型车',
    '大型车',

    // === 汽车品牌 ===
    '特斯拉',
    '比亚迪',
    '蔚来',
    '小鹏',
    '理想',
    '奔驰',
    '宝马',
    '奥迪',
    '丰田',
    '本田',
    '大众',
    '吉利',
    '长城',
    '红旗',
    '小米',
    '华为',
    '极氪',
    '岚图',
    '问界',
    '阿维塔',
    '智己',
    '飞凡',

    // === 核心技术系统 ===
    '自动驾驶',
    '智能驾驶',
    '车联网',
    '汽车科技',
    '新能源',
    '充电桩',
    '电池技术',
    '发动机',
    '变速箱',
    '悬架',
    '制动系统',
    '刹车系统',
    '转向系统',
    '传动系统',
    '冷却系统',
    '排气系统',
    '燃油系统',
    '电气系统',
    '空调系统',
    '安全气囊',
    'ABS',
    'ESP',
    'EBD',

    // === 动力与能源 ===
    '电机',
    '电控',
    '电池',
    '锂电池',
    '磷酸铁锂',
    '三元锂',
    '固态电池',
    '超充',
    '快充',
    '慢充',
    '换电',
    '续航',
    '里程',
    '能耗',
    '功率',
    '扭矩',
    '马力',
    '涡轮增压',
    '自然吸气',

    // === 智能化技术 ===
    '激光雷达',
    '毫米波雷达',
    '摄像头',
    '传感器',
    '芯片',
    '算力',
    'OTA',
    '车机',
    '中控屏',
    'HUD',
    '语音识别',
    '人机交互',
    '座舱',
    '智能座舱',
    '辅助驾驶',
    'L2',
    'L3',
    'L4',
    'L5',

    // === 汽车服务与产业 ===
    '汽车销量',
    '车展',
    '上市',
    '召回',
    '保养',
    '维修',
    '保险',
    '二手车',
    '车贷',
    '租车',
    '共享汽车',
    '汽车金融',
    '汽车后市场',
    '4S店',
    '经销商',

    // === 政策与标准 ===
    '国六',
    '双积分',
    '补贴',
    '购置税',
    '车牌',
    '限行',
    '环保',
    '排放',
    '碳中和',
    '绿牌',
    '蓝牌'
];

// 缓存配置
const CACHE_KEY = 'ai_news_cache';
const CACHE_DURATION = 30 * 60 * 1000; // 30分钟缓存

class NewsService {
    constructor() {
        this.baseUrl = 'https://orz.ai/api/v1/dailynews';
    }

    /**
     * 获取指定平台的新闻数据
     * @param {string} platform - 平台标识
     * @returns {Promise<Array>} 新闻文章数组
     */
    async fetchPlatformNews(platform) {
        try {
            const response = await fetch(`${this.baseUrl}/?platform=${platform}`, {
                method: 'GET',
                headers: { 'Content-Type': 'application/json' },
                signal: AbortSignal.timeout(10000)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const data = await response.json();

            if (data.status === '200' && data.data) {
                return this.transformNewsData(data.data, platform);
            }
            return [];
        } catch (error) {
            console.error(`获取 ${platform} 新闻失败:`, error);
            return [];
        }
    }

    /**
     * 转换新闻数据格式
     * @param {Array} rawData - 原始数据
     * @param {string} platform - 平台标识
     * @returns {Array} 格式化的新闻数据
     */
    transformNewsData(rawData, platform) {
        console.log(rawData, 'rawData');

        const platformConfig = PLATFORMS[platform];

        return rawData.map((item, index) => ({
            id: `${platform}_${Date.now()}_${index}`,
            title: item.title || '无标题',
            url: item.url || '#',
            date: item.publish_time || new Date(),
            source: platformConfig?.name || platform
        }));
    }

    /**
     * 过滤包含汽车关键词的文章
     * @param {Array} articles - 文章数组
     * @returns {Array} 过滤后的文章数组
     */
    filterCarRelatedArticles(articles) {
        return articles.filter(article => {
            const title = (article.title || '').toLowerCase();
            const content = (article.content || '').toLowerCase();

            // 检查标题是否包含任何汽车关键词
            return CAR_KEYWORDS.some(keyword => {
                const keywordLower = keyword.toLowerCase();
                return title.includes(keywordLower) || content.includes(keywordLower);
            });
        });
    }

    /**
     * 简单缓存管理
     */
    getCache() {
        try {
            const cached = localStorage.getItem(CACHE_KEY);
            if (cached) {
                const data = JSON.parse(cached);
                if (Date.now() - data.timestamp < CACHE_DURATION) {
                    // 恢复Date对象
                    const articles = data.articles.map(article => ({
                        ...article,
                        date: new Date(article.date)
                    }));
                    return articles;
                }
                localStorage.removeItem(CACHE_KEY);
            }
        } catch (error) {
            console.error('缓存读取失败:', error);
        }
        return null;
    }

    setCache(articles) {
        try {
            const data = {
                articles,
                timestamp: Date.now()
            };
            localStorage.setItem(CACHE_KEY, JSON.stringify(data));
        } catch (error) {
            console.error('缓存存储失败:', error);
        }
    }

    /**
     * 获取AI新闻
     * @returns {Promise<Array>} AI新闻文章数组
     */
    async getAINews() {
        // 先尝试从缓存获取
        const cached = this.getCache();
        if (cached) {
            console.log('从缓存获取AI新闻数据');
            return cached;
        }

        console.log('开始获取最新AI新闻数据...');

        try {
            // 并发获取多个平台的数据
            const platforms = Object.keys(PLATFORMS);
            const promises = platforms.map(platform => this.fetchPlatformNews(platform));
            const results = await Promise.allSettled(promises);

            // 合并所有成功的结果
            let allArticles = [];
            results.forEach(result => {
                if (result.status === 'fulfilled' && result.value) {
                    allArticles = allArticles.concat(result.value);
                }
            });

            // 过滤包含汽车关键词的文章
            const filteredArticles = this.filterCarRelatedArticles(allArticles);

            // 按时间排序，限制数量
            const finalArticles = filteredArticles.sort((a, b) => b.date - a.date).slice(0, 30);

            // 存储到缓存
            this.setCache(finalArticles);

            return finalArticles;
        } catch (error) {
            console.error('获取AI新闻失败:', error);
            return [];
        }
    }

    /**
     * 清空缓存
     */
    clearCache() {
        try {
            localStorage.removeItem(CACHE_KEY);
            console.log('新闻缓存已清空');
        } catch (error) {
            console.error('清空缓存失败:', error);
        }
    }
}

// 创建单例实例
const newsService = new NewsService();
export default newsService;
