<template>
    <section class="chat-content">
        <div class="top-header-container bg-white pl-3 pr-3 pt-2 pb-2 m-3 rounded-xl shadow-sm">
            <!-- 添加模块标题展示 -->
            <div class="module-title-container" v-if="examModuleName">
                <div class="module-title">
                    <span class="title-prefix">当前训练：</span>
                    <span class="module-name">{{ examModuleName }}</span>
                </div>
                <!-- 添加退出练习按钮 -->
                <ExitPracticeButton
                    @pausePractice="handlePausePractice"
                    @endPractice="handleEndPractice"
                />
                <!-- 添加设置面板组件 -->
                <SettingsPanel ref="settingsPanel" />
            </div>

            <!-- 添加进度条组件，并监听里程碑点击事件 -->
            <ExamProgress
                v-if="questionCountInfo.totalCount"
                :current-question="Number(currentQuestionNumber)"
                :total-questions="Number(questionCountInfo.totalCount)"
                :with-title="!examModuleName"
                :show-milestones="settingsPanel?.showMilestones"
                @milestone-click="handleMilestoneClick"
            />
        </div>

        <!-- PC端提示弹窗 -->
        <PCQrcodeDialog
            v-model:visible="showPCDialog"
            :show-back-button="true"
            :show-close-button="false"
        />

        <!-- 使用 LoadingState 组件 -->
        <LoadingState v-if="isLoading" :text="loadingText" />

        <div class="chat-content-inner">
            <div class="conversation" :class="{ 'has-voice-input': showVoiceInput }">
                <section class="item" v-for="question in chatStore.messages" :key="question.id">
                    <div class="item-right">
                        <!-- AI提问部分 -->
                        <div class="response">
                            <div class="ai-question">
                                <div class="ai-avatar">AI</div>
                                <div class="question-content">
                                    <!-- 音频播放器部分 -->
                                    <AudioPlayer
                                        v-if="question.questionTextTtsUrl"
                                        :audio-url="getFullAudioUrl(question.questionTextTtsUrl)"
                                        :text="question.questionText"
                                    />
                                </div>
                            </div>
                        </div>

                        <!-- 用户回答列表 -->
                        <template v-if="question.paperAnswers && question.paperAnswers.length">
                            <div
                                class="user-answer"
                                v-for="answer in question.paperAnswers"
                                :key="answer.id"
                            >
                                <div class="user-avatar">Me</div>
                                <div class="content">
                                    <div class="answer-content">
                                        <!-- 修改删除按钮，添加轻量级确认机制 -->
                                        <div
                                            v-if="question.paperQuestionStatus === 2"
                                            class="delete-action"
                                        >
                                            <!-- 普通状态的删除按钮 -->
                                            <div
                                                v-if="!answer.showDeleteConfirm"
                                                class="delete-btn"
                                                @click="handleDeleteAnswer(answer)"
                                                :class="{
                                                    'is-deleting': answer.isDeleting
                                                }"
                                            >
                                                <i class="pi pi-trash"></i>
                                            </div>

                                            <!-- 确认删除状态 -->
                                            <div v-else class="delete-confirm">
                                                <div
                                                    class="confirm-btn confirm-yes"
                                                    @click="confirmDeleteAnswer(question, answer)"
                                                >
                                                    <i class="pi pi-check"></i>
                                                </div>
                                                <div
                                                    class="confirm-btn confirm-no"
                                                    @click="cancelDeleteAnswer(answer)"
                                                >
                                                    <i class="pi pi-times"></i>
                                                </div>
                                            </div>
                                        </div>

                                        <DingAudioPlayer
                                            v-if="answer.dingMediaId"
                                            theme="green"
                                            :audio-id="answer.dingMediaId"
                                            :audio-duration="answer.duration"
                                            :audio-text="answer.answerAudioAsrRepaired"
                                        />
                                    </div>

                                    <div class="translate-btn-wrapper">
                                        <!-- 修改转文字按钮位置，移到音频下方 -->
                                        <div
                                            class="translate-btn"
                                            @click="handleTranslate(answer.id)"
                                            :class="{
                                                'is-translating': answer.isTranslating
                                            }"
                                        >
                                            <i class="pi pi-language" />
                                            <span>{{
                                                answer.isTranslating
                                                    ? EXAM_TEXT.STATUS.CONVERTING
                                                    : EXAM_TEXT.STATUS.CONVERT_TEXT
                                            }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </template>

                        <!-- 修改评价展示部分，添加获取评价按钮 -->
                        <template v-if="question.paperQuestionStatus === 3 && question.isAnalyzing">
                            <!-- 获取评价中 -->
                            <AIAnalyzing />
                        </template>

                        <!-- 新增：评价展示部分 -->
                        <template v-if="[4].includes(question.paperQuestionStatus)">
                            <Assessment
                                v-if="
                                    question.paperCriterionAssessments &&
                                    question.paperCriterionAssessments.length
                                "
                                :assessments="question.paperCriterionAssessments"
                                class="assessment-wrapper"
                            />
                        </template>

                        <!-- 新增：评价接口报错了，显示重试按钮 -->
                        <template
                            v-if="
                                [3].includes(question.paperQuestionStatus) &&
                                question.showRetryAnalysis
                            "
                        >
                            <!-- 获取评价失败时显示错误信息和重试按钮 -->
                            <div class="retry-analysis-wrapper">
                                <p class="error-message">
                                    {{ EXAM_TEXT.ERRORS.REQUEST_FAILED }}
                                </p>
                                <Button
                                    class="retry-btn"
                                    @click="showGetScoreAnimation(question)"
                                    severity="primary"
                                >
                                    <i class="pi pi-refresh" style="margin-right: 8px"></i>
                                    {{ EXAM_TEXT.BUTTONS.RETRY }}
                                </Button>
                            </div>
                        </template>

                        <!-- 修改完成按钮的条件渲染逻辑 -->

                        <template v-if="[2].includes(question.paperQuestionStatus)">
                            <div class="complete-button-wrapper">
                                <!-- 添加重练按钮 -->
                                <Button
                                    class="retry-btn"
                                    @click="handleRetryQuestion(question)"
                                    :loading="question.isRetrying"
                                    severity="secondary"
                                >
                                    <i class="pi pi-refresh" style="margin-right: 8px"></i>
                                    {{
                                        question.isRetrying
                                            ? EXAM_TEXT.BUTTONS.RETRYING
                                            : EXAM_TEXT.BUTTONS.RETRY_QUESTION
                                    }}
                                </Button>

                                <Button
                                    class="complete-btn"
                                    @click="handleCompleteAnswer()"
                                    :loading="question.isCompletingAnswer"
                                    severity="primary"
                                >
                                    <i class="pi pi-check" style="margin-right: 8px"></i>
                                    {{
                                        question.isCompletingAnswer
                                            ? EXAM_TEXT.BUTTONS.SUBMITTING
                                            : EXAM_TEXT.BUTTONS.COMPLETE_ANSWER
                                    }}
                                </Button>
                            </div>
                        </template>

                        <!-- 新增：查看下一题按钮 -->

                        <template
                            v-if="question.paperQuestionStatus === 4 && isLastQuestion(question)"
                        >
                            <div class="complete-button-wrapper">
                                <Button
                                    class="complete-btn next-btn"
                                    @click="
                                        isAllQuestionsCompleted
                                            ? handleViewResult()
                                            : handleNextQuestion()
                                    "
                                    severity="primary"
                                >
                                    <i
                                        :class="[
                                            isAllQuestionsCompleted
                                                ? 'pi pi-chart-bar'
                                                : 'pi pi-arrow-right'
                                        ]"
                                        style="margin-right: 8px"
                                    ></i>
                                    {{
                                        isAllQuestionsCompleted
                                            ? EXAM_TEXT.BUTTONS.VIEW_RESULT
                                            : EXAM_TEXT.BUTTONS.NEXT_QUESTION
                                    }}
                                </Button>
                            </div>
                        </template>
                    </div>
                </section>
            </div>

            <!-- 修改语音输入区域的条件渲染 -->
            <div class="user-input" v-if="showVoiceInput">
                <VoiceInput @recording-complete="handleRecordingComplete" />
            </div>
        </div>
    </section>
</template>

<script setup>
import * as dd from 'dingtalk-jsapi'; // 直接导入
import { ref, computed, onMounted, onBeforeUnmount, nextTick } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import useChatStore from '@/stores/chat';
import useTrainerStore from '@/stores/trainer';
import { getFullAudioUrl } from '@/utils/trainer';
import AudioPlayer from '@/components/AudioPlayer/index.vue';
import VoiceInput from '@/components/VoiceInput/index.vue';
import Assessment from '@/views/smart-trainer/components/Assessment.vue';
import AIAnalyzing from '@/views/smart-trainer/components/AIAnalyzing.vue';
import LoadingState from '@/components/common/LoadingState.vue';
import PCQrcodeDialog from '@/components/PCQrcodeDialog.vue';
import { TRAINING_TEXT_SCHEMA2 as EXAM_TEXT } from '../../constants/text';
import ExamProgress from '@/views/smart-trainer/components/ExamProgress.vue';
import DingAudioPlayer from '@/components/DingAudioPlayer/index.vue';
import audioManager from '@/utils/audioManager';
import { useToast } from 'primevue/usetoast';
import SettingsPanel from '@/views/smart-trainer/components/SettingsPanel.vue';
import ExitPracticeButton from '@/views/smart-trainer/components/ExitPracticeButton.vue';
import { pollAsyncResult } from '@/utils/polling';
import { isDingTalkPC, isDingTalk } from '@/utils/index';

// 路由相关
const route = useRoute();
const router = useRouter();
const toast = useToast();
const chatStore = useChatStore();
const trainerStore = useTrainerStore();

// 添加 loading 状态控制
const isLoading = ref(false);
const loadingText = ref(EXAM_TEXT.STATUS.LOADING_EXAM);

// Loading 配置
const loadingConfig = ref({
    visible: false,
    status: 'loading',
    message: EXAM_TEXT.STATUS.LOADING_EXAM
});

// 统一的 loading 管理函数
const setLoading = (loading, message = EXAM_TEXT.STATUS.LOADING_EXAM) => {
    isLoading.value = loading;
    loadingText.value = message;
    loadingConfig.value.visible = loading;
    loadingConfig.value.status = loading ? 'loading' : 'loaded';
    loadingConfig.value.message = message;

    if (!loading) {
        // 延迟隐藏 loading
        setTimeout(() => {
            loadingConfig.value.visible = false;
        }, 300);
    }
};

const showPCDialog = ref(false);

if (isDingTalk()) {
    dd.setNavigationTitle({
        title: EXAM_TEXT.PAGE_TITLES.SMART_TRAINING
    });
}

const currentQuestionNumber = computed(() => {
    if (!chatStore.messages.length) {
        return 1;
    }
    return chatStore.messages.filter(q => [3, 4].includes(Number(q.paperQuestionStatus))).length;
});

// 添加新的响应式变量来存储各种状态的题目数
const questionCountInfo = ref({
    totalCount: 0,
    createdCount: 0,
    questionedCount: 0,
    doingCount: 0,
    completedCount: 0,
    assessedCount: 0
});

const totalQuestions = ref(0);
// 封装获取题目数的方法
async function fetchQuestionCount(paperId) {
    try {
        const result = await trainerStore.getQuestionCount({ paperId });
        if (result) {
            // 更新题目数信息
            questionCountInfo.value = {
                totalCount: result.totalCount || 0,
                createdCount: result.createdCount || 0,
                questionedCount: result.questionedCount || 0,
                doingCount: result.doingCount || 0,
                completedCount: result.completedCount || 0,
                assessedCount: result.assessedCount || 0
            };

            // 更新总题目数
            totalQuestions.value = result.totalCount || 0;
        }
    } catch (error) {
        console.error(EXAM_TEXT.ERRORS.REQUEST_FAILED, error.message);
    }
}

// 添加模块名称的响应式变量
const examModuleName = ref('');
// 封装获取试卷详情的方法
async function getPaperDetail(paperId) {
    try {
        const result = await trainerStore.getPaperDetail({ paperId });
        if (result) {
            // 更新模块名称
            examModuleName.value = result.name || '';
            return result;
        }
    } catch (error) {
        console.error(EXAM_TEXT.ERRORS.REQUEST_FAILED, error.message);
    }
    return null;
}

// 处理录音完成事件
const handleRecordingComplete = async recordingResult => {
    try {
        setLoading(true, EXAM_TEXT.STATUS.LOADING_UPLOAD);

        // 通过 currentQuestionId 查找当前问题
        const currentQuestion = chatStore.messages.find(
            question => question.id === trainerStore.currentQuestionId
        );

        if (!currentQuestion) {
            return;
        }

        // 调用语音回答接口
        const result = await trainerStore.sendDingAudioAnswer({
            paperQuestionId: trainerStore.currentQuestionId,
            remoteUrl: recordingResult.remoteUrl,
            mediaId: recordingResult.mediaId,
            duration: recordingResult.duration
        });

        if (result) {
            // 直接将新的回答添加到当前问题的 paperAnswers 数组中
            if (!currentQuestion.paperAnswers) {
                currentQuestion.paperAnswers = [];
            }
            currentQuestion.paperAnswers.push(result);

            // 更新问题状态为【已回答、待完成】（2）
            currentQuestion.paperQuestionStatus = 2;

            // 触发视图更新并滚动到底部
            nextTick(() => {
                setConversationToBottom();
            });
        }
    } catch (error) {
        console.error(EXAM_TEXT.ERRORS.REQUEST_FAILED, error.message);
    } finally {
        setLoading(false);
    }
};

// 添加自动播放音频的函数
/**
 * 自动播放指定问题的音频
 * @param {Object} question - 需要播放音频的问题对象，如果不传则播放最后一个问题
 * @param {Object} options - 自动播放选项
 * @param {boolean} options.autoPlay - 是否自动播放，默认为true
 * @param {number} options.delay - 延迟播放时间(毫秒)，默认为500
 */
const autoPlayQuestionAudio = (question = null, options = {}) => {
    // 默认配置
    const defaultOptions = {
        autoPlay: true,
        delay: 2000
    };

    // 合并配置
    const config = { ...defaultOptions, ...options };

    // 如果不自动播放，直接返回
    if (!config.autoPlay) {
        return;
    }

    nextTick(() => {
        setTimeout(() => {
            // 获取所有音频播放器
            const audioPlayers = document.querySelectorAll('.question-content .audio-wrapper');

            if (audioPlayers.length > 0) {
                let targetAudioPlayer = null;

                if (question) {
                    // 如果指定了问题，查找该问题对应的音频播放器
                    // 首先找到问题对应的DOM元素
                    const questionItems = document.querySelectorAll('.conversation .item');

                    // 遍历所有问题项，找到匹配当前问题ID的元素
                    for (let i = 0; i < questionItems.length; i++) {
                        const item = questionItems[i];
                        // 在每个问题的上下文中查找音频播放器
                        const audioPlayer = item.querySelector('.question-content .audio-wrapper');

                        if (audioPlayer) {
                            // 检查这个问题项是否对应我们要找的问题
                            // 可以通过索引位置判断（因为问题顺序与messages数组一致）
                            if (chatStore.messages[i] && chatStore.messages[i].id === question.id) {
                                targetAudioPlayer = audioPlayer;
                                break;
                            }
                        }
                    }
                } else {
                    // 否则获取最后一个音频播放器（最新的问题）
                    targetAudioPlayer = audioPlayers[audioPlayers.length - 1];
                }

                // 触发点击事件来播放音频
                if (targetAudioPlayer) {
                    targetAudioPlayer.querySelector('.audio-bubble')?.click();
                }
            }
        }, config.delay);
    });
};

// 修改获取下一道题目的函数，使用新的自动播放函数
const getNextQuestion = async () => {
    try {
        setLoading(true, EXAM_TEXT.STATUS.LOADING_QUESTION);

        const question = await trainerStore.getQuestion({
            paperId: trainerStore.currentPaperId
        });

        if (question) {
            chatStore.messages.push({
                ...question,
                type: 'question'
            });

            // 等待 DOM 更新完成后播放音频，使用配置的延迟时间
            nextTick(() => {
                setConversationToBottom();
                autoPlayQuestionAudio(null, {
                    autoPlay: trainerStore.autoPlayAudio,
                    delay: trainerStore.audioPlayDelay
                });
            });
        }
    } catch (error) {
        console.error(EXAM_TEXT.ERRORS.REQUEST_FAILED, error.message);
    } finally {
        setLoading(false);
    }
};

// 重置所有状态
const resetAllStates = () => {
    // 清空消息记录
    chatStore.clearMessages();

    // 重置训练相关状态
    trainerStore.$reset(); // 重置整个 trainer store 到初始状态

    // 重置加载状态
    setLoading(false, EXAM_TEXT.STATUS.LOADING_EXAM);
};

// 添加控制 VoiceInput 显示的计算属性
const showVoiceInput = computed(() => {
    if (!chatStore.messages.length) {
        return false;
    }

    // 获取当前问题（最后一个问题）
    const currentQuestion = chatStore.messages[chatStore.messages.length - 1];

    // paperQuestionStatus 枚举： 0：已创建；1：已提问，2：已回答、未完成；3:回答已完成；4:AI已评价
    // 只在问题状态0 、1、2 场景中显示 录音按钮
    return [0, 1, 2].includes(currentQuestion.paperQuestionStatus);
});

// 修改获取消息列表的函数，使用新的自动播放函数
async function getMessageList(paperId) {
    try {
        isLoading.value = true;
        loadingText.value = EXAM_TEXT.STATUS.LOADING_HISTORY;

        const result = await trainerStore.getHistoryMessageList({
            paperId,
            pageIndex: 1,
            pageSize: 1000
        });

        if (result && result.list && result.list.length > 0) {
            chatStore.messages = result.list.reverse().map(question => ({
                ...question,
                isCompletingAnswer: false
            }));

            // 获取最后一个问题
            const lastQuestion = chatStore.messages[chatStore.messages.length - 1];

            // 如果最后一个问题的状态是 0、1 或 2，自动播放音频
            if ([0, 1, 2].includes(lastQuestion.paperQuestionStatus)) {
                nextTick(() => {
                    setConversationToBottom();
                    autoPlayQuestionAudio(lastQuestion, {
                        autoPlay: trainerStore.autoPlayAudio,
                        delay: trainerStore.audioPlayDelay
                    });
                });
            } else {
                setConversationToBottom();
            }
            return true;
        }
        return false;
    } catch (error) {
        console.error(EXAM_TEXT.ERRORS.REQUEST_FAILED, error.message);
        return false;
    } finally {
        isLoading.value = false;
    }
}

// 修改创建试卷的函数
const createPaper = async (examId, force = false) => {
    isLoading.value = true;
    loadingText.value = EXAM_TEXT.STATUS.LOADING_CREATE;

    try {
        const result = await trainerStore.createPaper({ examId, force });
        if (result) {
            // 先获取题目数
            await fetchQuestionCount(trainerStore.currentPaperId);

            // 获取试卷详情（包含模块名称）
            await getPaperDetail(trainerStore.currentPaperId);

            // 再获取消息列表
            const hasHistory = await getMessageList(trainerStore.currentPaperId);
            if (!hasHistory) {
                await getNextQuestion();
            }
        } else {
            // 创建失败，返回上一页
            toast.add({
                severity: 'error',
                summary: '错误',
                detail: EXAM_TEXT.ERRORS.CREATE_FAILED,
                life: 3000
            });
            router.back();
        }
    } catch (error) {
        console.error(EXAM_TEXT.ERRORS.REQUEST_FAILED, error.message);
        router.back();
        return false;
    } finally {
        isLoading.value = false;
    }
};

// 初始化函数
const init = async () => {
    // 检查是否为PC设备
    if (isDingTalkPC()) {
        showPCDialog.value = true;
        return;
    }

    isLoading.value = true;
    loadingText.value = EXAM_TEXT.STATUS.LOADING_INIT;

    try {
        const { examId, paperId, retry } = route.query;
        // 如果有 paperId，获取题目数
        if (paperId) {
            trainerStore.setCurrentPaperId(paperId);

            const hasHistory = await getMessageList(paperId);
            if (!hasHistory) {
                getNextQuestion();
            }

            await fetchQuestionCount(paperId);
            await getPaperDetail(paperId);

            return;
        }

        // 处理 examId 的情况，直接创建新试卷
        if (examId) {
            // 如果指定了 retry 参数，强制创建新试卷
            const force = retry === 'true';
            await createPaper(examId, force);
        }
    } catch (error) {
        console.error(EXAM_TEXT.ERRORS.REQUEST_FAILED, error.message);
    } finally {
        isLoading.value = false;
    }
};

// 滚动到底部
const setConversationToBottom = () => {
    nextTick(() => {
        const conversationEl = document.querySelector('.chat-content-inner .conversation');
        if (conversationEl) {
            // 计算实际需要滚动的高度
            const scrollHeight = conversationEl.scrollHeight;
            const clientHeight = conversationEl.clientHeight;
            setTimeout(() => {
                // 确保滚动到最底部，考虑padding-bottom的高度
                conversationEl.scrollTop = scrollHeight - clientHeight + 100000; // 增加额外的滚动距离
            }, 500);
        }
    });
};

// 添加轮询函数
async function pollAnswerStatus(answerId, maxAttempts = 20, interval = 3000) {
    let attempts = 0;

    // 使用递归函数替代 while 循环中的 await
    const poll = async () => {
        if (attempts >= maxAttempts) {
            throw new Error(EXAM_TEXT.ERRORS.CONVERT_TIMEOUT);
        }

        try {
            const result = await trainerStore.getAnswerAsr({
                paperAnswerId: answerId
            });

            // 如果状态为2，说明ASR修复完成
            if (result?.paperAnswerStatus === 2) {
                return result;
            }

            // 等待指定时间后继续轮询
            attempts++;
            await new Promise(resolve => setTimeout(resolve, interval));
            return await poll();
        } catch (error) {
            console.error(EXAM_TEXT.ERRORS.REQUEST_FAILED, error);
            throw new Error(EXAM_TEXT.ERRORS.CONVERT_TIMEOUT);
        }
    };

    return await poll();
}

// 修改处理转文字的函数
async function handleTranslate(answerId) {
    // 查找对应的 answer
    const answer = chatStore.messages.reduce((found, question) => {
        if (found) {
            return found;
        }
        return question.paperAnswers?.find(a => a.id === answerId);
    }, null);

    if (!answer) {
        return;
    }

    // 设置转文字中状态
    answer.isTranslating = true;
    answer.answerAudioAsrRepaired = '转文字中...';

    try {
        // 开始轮询转文字状态
        const result = await pollAnswerStatus(answerId);

        // 更新转文字结果
        answer.answerAudioAsrRepaired = result?.answerAudioAsrRepaired || '未识别到内容';
    } catch (error) {
        console.error(EXAM_TEXT.ERRORS.REQUEST_FAILED, error.message);
        answer.answerAudioAsrRepaired = '语音转文字失败';
    } finally {
        answer.isTranslating = false;
    }
}

// 判断是否为最后一个问题
const isLastQuestion = question => {
    const lastQuestion = chatStore.messages[chatStore.messages.length - 1];
    return question.id === lastQuestion.id;
};

// 处理下一题按钮点击
const handleNextQuestion = () => {
    getNextQuestion();
};

// 修改 handleCompleteAnswer 函数
async function handleCompleteAnswer() {
    const question = chatStore.messages.find(q => q.id === trainerStore.currentQuestionId);
    if (!question) {
        return;
    }

    try {
        question.isCompletingAnswer = true;
        question.showRetryAnalysis = false; // 重置重试状态

        const result = await trainerStore.completeAnswer({
            paperQuestionId: trainerStore.currentQuestionId
        });

        if (result) {
            question.paperQuestionStatus = 3;

            showGetScoreAnimation(question);
        } else {
            toast.add({
                severity: 'error',
                summary: '错误',
                detail: EXAM_TEXT.ERRORS.REQUEST_FAILED
            });
        }
    } catch (error) {
        console.error(EXAM_TEXT.ERRORS.REQUEST_FAILED, error.message);
    } finally {
        question.isCompletingAnswer = false;
    }
}

// 显示评价获取动画
const showGetScoreAnimation = async question => {
    question.isAnalyzing = true;
    question.showRetryAnalysis = false;
    nextTick(() => {
        setConversationToBottom();
    });
    await handleGetCommentAndScore(question);
};

// 修改获取评价的函数
async function handleGetCommentAndScore(question) {
    try {
        // 使用轮询工具获取结果
        const result = await pollAsyncResult(
            trainerStore.getCommentAndScore,
            { paperQuestionId: trainerStore.currentQuestionId },
            {
                onPolling: count => {
                    console.log(`正在获取结果，第${count}次轮询`);
                },
                onSuccess: data => {
                    console.log('成功获取结果数据');
                },
                onError: error => {
                    handleError(question);
                    console.error('获取评价结果失败: from onError', error);
                },
                onTimeout: () => {
                    handleError(question);
                    console.error('获取评价结果超时 from onTimeout');
                }
            }
        );
        // 如果成功获取结果
        if (result) {
            question.isAnalyzing = false;
            question.showRetryAnalysis = false;
            question.paperCriterionAssessments = result.result;
            question.paperQuestionStatus = 4;

            nextTick(() => {
                setConversationToBottom();
            });
        } else {
            console.error('获取评价结果失败: from handleGetCommentAndScore1');
            handleError(question);
        }
    } catch (error) {
        console.error('获取评价结果失败: from handleGetCommentAndScore2');
        handleError(question);
    }
}

// 处理获取评价接口结果失败
const handleError = question => {
    console.error('获取评价结果失败: from handleError');
    question.isAnalyzing = false;
    question.showRetryAnalysis = true;
    nextTick(() => {
        setConversationToBottom();
    });
};

// 相当于是暂存训练结果，下次还能继续练
const handlePausePractice = () => {
    router.back();
};

// 不保存记录，直接退出了
const handleEndPractice = async () => {
    await trainerStore.deletePractice({
        paperId: trainerStore.currentPaperId
    });
    router.back();
};

// 新增计算属性，判断是否已完成所有题目
const isAllQuestionsCompleted = computed(() => {
    return currentQuestionNumber.value === questionCountInfo.value.totalCount;
});

// 新增查看结果的处理函数
const handleViewResult = () => {
    router.replace(
        `/smart-trainer/exam/result?paperId=${trainerStore.currentPaperId}&examId=${route.query.examId}`
    );
};

// 处理删除回答
function handleDeleteAnswer(answer) {
    // 如果正在删除中，不执行操作
    if (answer.isDeleting) {
        return;
    }

    // 使用轻量级的确认方式
    answer.showDeleteConfirm = true;
}

// 添加实际删除操作的函数
async function confirmDeleteAnswer(question, answer) {
    try {
        // 重置确认状态
        answer.showDeleteConfirm = false;
        // 设置删除中状态
        answer.isDeleting = true;

        // 调用删除接口
        const result = await trainerStore.deleteAnswer({
            paperAnswerId: answer.id
        });

        if (result) {
            // 从界面上移除该回答
            const answerIndex = question.paperAnswers.findIndex(a => a.id === answer.id);
            if (answerIndex !== -1) {
                question.paperAnswers.splice(answerIndex, 1);
            }

            // 检查问题是否还有回答，如果没有则重置问题状态为已提问（1）
            if (!question.paperAnswers || question.paperAnswers.length === 0) {
                question.paperQuestionStatus = 1; // 重置为已提问状态
            }

            // 显示成功提示
            toast.add({
                severity: 'success',
                summary: EXAM_TEXT.STATUS.SUCCESS,
                detail: EXAM_TEXT.STATUS.DELETE_SUCCESS,
                life: 2000
            });
        }
    } catch (error) {
        console.error(EXAM_TEXT.ERRORS.DELETE_FAILED, error.message);

        // 显示错误提示
        toast.add({
            severity: 'error',
            summary: EXAM_TEXT.STATUS.ERROR,
            detail: EXAM_TEXT.ERRORS.DELETE_FAILED,
            life: 3000
        });
    } finally {
        // 重置删除中状态
        answer.isDeleting = false;
    }
}

// 取消删除操作
function cancelDeleteAnswer(answer) {
    answer.showDeleteConfirm = false;
}

// 修改重练本题的处理函数，使用新的自动播放函数
async function handleRetryQuestion(question) {
    if (question.isRetrying) {
        return;
    }

    // 如果没有回答，不需要重练
    if (!question.paperAnswers || question.paperAnswers.length === 0) {
        return;
    }

    try {
        // 设置重练中状态
        question.isRetrying = true;

        // 调用批量删除接口
        const result = await trainerStore.deleteAnswerByQuestionId({
            paperQuestionId: question.id
        });

        if (result) {
            // 清空回答列表
            question.paperAnswers = [];

            // 重置问题状态为已提问（1）
            question.paperQuestionStatus = 1;

            // 自动播放问题音频，使用配置的延迟时间
            autoPlayQuestionAudio(question, {
                autoPlay: trainerStore.autoPlayAudio,
                delay: trainerStore.audioPlayDelay
            });
        }
    } catch (error) {
        console.error(EXAM_TEXT.ERRORS.RETRY_FAILED, error.message);

        // 显示错误提示
        toast.add({
            severity: 'error',
            summary: EXAM_TEXT.STATUS.ERROR,
            detail: EXAM_TEXT.ERRORS.RETRY_FAILED,
            life: 3000
        });
    } finally {
        question.isRetrying = false;
    }
}

// 添加设置面板引用
const settingsPanel = ref(null);

// 添加处理里程碑点击的函数
const handleMilestoneClick = milestone => {
    // 根据里程碑的 questionIndex 找到对应的题目元素
    const questionElements = document.querySelectorAll('.conversation .item');

    if (milestone.questionIndex < questionElements.length) {
        const targetElement = questionElements[milestone.questionIndex];

        // 平滑滚动到目标元素
        targetElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });

        // 添加高亮动画效果
        targetElement.classList.add('highlight-question');
        setTimeout(() => {
            targetElement.classList.remove('highlight-question');
        }, 2000);
    }
};

// loadingRef 已在上面声明

onMounted(() => {
    // 初始化存储
    trainerStore.initializeStorage();

    resetAllStates();

    nextTick(() => {
        init();
    });
});

// 在组件卸载前停止所有音频
onBeforeUnmount(() => {
    // 停止所有正在播放的音频
    audioManager.stopAll();

    // 重置所有状态
    resetAllStates();
});
</script>

<style lang="scss" scoped>
.chat-content {
    width: 100%;
    height: 100%;
    flex: 1;
    overflow: hidden;
    background-color: $system-grouped-background-primary;
    display: flex;
    flex-direction: column;
    .top-header-container {
        border-radius: 12px;
    }

    /* 添加模块标题样式 */
    .module-title-container {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        .module-title {
            color: $label-primary;
            display: flex;
            align-items: center;
            flex: 1;
            overflow: hidden;

            .title-prefix {
                font-size: 12px;
                color: $label-secondary;
                font-weight: 500;
                margin-right: 8px;
            }

            .module-name {
                font-size: 14px;
                font-weight: 600;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }

        /* 添加退出按钮和设置面板的布局样式 */
        :deep(.action-btn) {
            margin-right: 20px;
        }
    }

    .chat-content-inner {
        width: 100%;
        flex: 1;
        overflow: hidden;
        position: relative;
        display: flex;
        flex-direction: column;
        .conversation {
            position: relative;
            flex: 1;
            overflow-y: auto;
            scroll-behavior: smooth;
            padding-bottom: 120px; /* 增加底部内边距，确保内容不被输入框遮挡 */

            &.has-voice-input {
                padding-bottom: 120px; /* 当有语音输入按钮时增加底部间距 */
            }

            section.item {
                display: flex;
                flex-direction: column;
                justify-content: center;
                max-width: 1200px;
                margin: 0 auto;
                position: relative;
                padding: 0 14px;

                // 添加题号和分隔符样式
                .question-number-divider {
                    width: 100%;
                    max-width: 680px;
                    margin: 0 auto 16px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    padding: 0 12px;

                    .question-number {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin-bottom: 8px;

                        .number {
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            width: 28px;
                            height: 28px;
                            background: $system-blue;
                            color: $system-background-primary;
                            font-size: 14px;
                            font-weight: 600;
                            border-radius: 50%;
                            box-shadow: 0 2px 6px rgba(22, 119, 255, 0.2);
                        }

                        .label {
                            margin-left: 8px;
                            font-size: 14px;
                            color: $label-secondary;
                            font-weight: 500;
                        }
                    }

                    .divider-line {
                        width: 100%;
                        height: 1px;
                        background: linear-gradient(
                            90deg,
                            rgba($system-blue, 0.05) 0%,
                            rgba($system-blue, 0.3) 50%,
                            rgba($system-blue, 0.05) 100%
                        );
                    }
                }

                .item-right {
                    width: 100%;
                    max-width: 680px;
                    margin: 0 auto;

                    .user-answer {
                        position: relative;
                        display: flex;
                        flex-direction: row-reverse;
                        align-items: flex-start;
                        margin-top: 20px;

                        .user-avatar {
                            width: 32px;
                            height: 32px;
                            border-radius: 50%;
                            background-color: $system-green;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-size: 14px;
                            color: white;
                            font-weight: 500;
                            flex-shrink: 0;
                            margin-left: 8px;
                        }

                        .content {
                            word-break: break-all;
                            white-space: pre-wrap;
                            flex: 1;
                            position: relative;

                            .answer-content {
                                display: flex;
                                align-items: center;
                                justify-content: flex-end;
                            }

                            .translate-btn-wrapper {
                                display: flex;
                                margin-top: 8px;
                            }

                            .translate-btn {
                                margin-left: auto;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                padding: 4px 10px;
                                border-radius: 12px;
                                background: linear-gradient(
                                    135deg,
                                    $system-gray 0%,
                                    $system-gray2 100%
                                );
                                color: $system-background-primary;
                                font-size: 12px;
                                font-weight: 500;
                                cursor: pointer;
                                transition: all 0.3s ease;
                                box-shadow: 0 2px 4px rgba($system-gray, 0.15);
                                user-select: none;

                                i {
                                    font-size: 12px;
                                    margin-right: 4px;
                                    transition: transform 0.3s ease;
                                }

                                &:active {
                                    transform: scale(0.96);
                                    background: linear-gradient(
                                        135deg,
                                        $system-gray2 0%,
                                        $system-gray3 100%
                                    );
                                }

                                &.is-translating {
                                    background: linear-gradient(
                                        135deg,
                                        $system-gray3 0%,
                                        $system-gray4 100%
                                    );
                                    i {
                                        animation: rotate 1.5s linear infinite;
                                    }
                                }
                            }
                        }
                    }
                    .assessment-wrapper {
                        margin: 20px 0;
                        position: relative;

                        // 添加上方的分隔线
                        &::before {
                            content: '';
                            position: absolute;
                            top: -10px;
                            left: 0;
                            right: 0;
                            height: 1px;
                            background: linear-gradient(
                                90deg,
                                rgba($system-blue, 0.05) 0%,
                                rgba($system-blue, 0.3) 50%,
                                rgba($system-blue, 0.05) 100%
                            );
                        }
                    }

                    .response {
                        .ai-question {
                            display: flex;
                            gap: 8px;
                            align-items: flex-start;
                            font-size: 14px;

                            .ai-avatar {
                                width: 32px;
                                height: 32px;
                                border-radius: 50%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                font-size: 16px;
                                color: #fff;
                                font-weight: 500;
                                flex-shrink: 0;
                                background-color: $system-blue;
                            }

                            .question-content {
                                flex: 1;
                                overflow: hidden;
                            }
                        }
                    }
                }
            }
        }

        .user-input {
            position: fixed; /* 改为绝对定位 */
            bottom: 10px; /* 固定在底部 */
            left: 0;
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10; /* 提高层级 */
            // 添加过渡动画
            transition: all 0.3s ease-in-out;
            background: #f4f7fc; /* 与页面背景色保持一致 */
            // 添加顶部毛玻璃效果渐变
            &::before {
                content: '';
                position: absolute;
                top: -20px;
                left: 0;
                right: 0;
                height: 20px;
                background: linear-gradient(to bottom, rgba(244, 247, 252, 0), #f4f7fc);
                pointer-events: none;
                z-index: 1;
            }

            // 添加进入和离开的动画
            &.v-enter-active,
            &.v-leave-active {
                transition: opacity 0.3s ease, transform 0.3s ease;
            }

            &.v-enter-from,
            &.v-leave-to {
                opacity: 0;
                transform: translateY(20px);
            }
        }
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

// 添加问题高亮动画
.conversation .item.highlight-question {
    animation: highlightQuestion 2s ease-in-out;
}

@keyframes highlightQuestion {
    0% {
        background-color: transparent;
    }
    25% {
        background-color: rgba(22, 119, 255, 0.1);
    }
    75% {
        background-color: rgba(22, 119, 255, 0.1);
    }
    100% {
        background-color: transparent;
    }
}

.complete-button-wrapper {
    display: flex;
    justify-content: center;
    padding: 24px 12px 16px;
    gap: 12px; /* 添加按钮之间的间距 */

    .complete-btn {
        background: linear-gradient(135deg, $system-blue 0%, $system-teal 100%);
        border: none;
        padding: 8px 20px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba($system-blue, 0.2);

        &:hover {
            background: linear-gradient(135deg, $system-indigo 0%, $system-blue 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba($system-blue, 0.3);
        }

        &:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba($system-blue, 0.2);
        }
    }

    /* 添加重练按钮样式 */
    .retry-btn {
        background: linear-gradient(135deg, $system-gray4 0%, $system-gray5 100%);
        border: none;
        padding: 8px 20px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 500;
        color: $label-secondary;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba($label-quaternary, 0.1);

        &:hover {
            background: linear-gradient(135deg, $system-gray3 0%, $system-gray4 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba($label-quaternary, 0.15);
        }

        &:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba($label-quaternary, 0.1);
        }

        i {
            transition: transform 0.3s ease;
        }

        &:loading {
            i {
                animation: rotate 1.5s linear infinite;
            }
        }
    }
}

// 添加重试按钮样式
.retry-analysis-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 16px;
    margin: 12px;
    background: rgba($system-red, 0.05);
    border-radius: 12px;
    border: 1px dashed rgba($system-red, 0.3);

    .error-message {
        color: $system-red;
        font-size: 14px;
        margin-bottom: 16px;
        display: flex;
        align-items: center;

        &::before {
            content: '\e90b'; /* 使用 PrimeIcons 的警告图标 */
            font-family: 'primeicons';
            margin-right: 8px;
            font-size: 16px;
            color: $system-red;
        }

        /* 添加轻微的文字阴影增强可读性 */
        text-shadow: 0 1px 0 rgba(255, 255, 255, 0.8);
    }

    .continue-btn {
        border-radius: 20px;
        font-size: 14px;
        font-weight: 500;
        background: linear-gradient(135deg, #1677ff 0%, #4096ff 100%);
        border: none;
        padding: 8px 20px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(22, 119, 255, 0.2);
        /* 添加按钮动画效果 */
        animation: buttonPulse 2s infinite;

        &:hover {
            background: linear-gradient(135deg, #0958d9 0%, #1677ff 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(22, 119, 255, 0.3);
        }

        &:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(22, 119, 255, 0.2);
            animation: none;
        }
    }
}

.get-assessment-btn {
    background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);

    &:hover {
        background: linear-gradient(135deg, #531dab 0%, #722ed1 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(114, 46, 209, 0.3);
    }

    &:active {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(114, 46, 209, 0.2);
    }
}

// 更新删除按钮样式
.delete-action {
    margin-right: 8px;
    flex-shrink: 0;

    .delete-btn {
        width: 28px;
        height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background: rgba($system-red, 0.1);
        color: $system-red;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.3s ease;

        i {
            font-size: 14px;
        }

        &:active {
            transform: scale(0.92);
            background: rgba($system-red, 0.2);
        }

        &.is-deleting {
            i {
                animation: rotate 1.5s linear infinite;
            }
        }
    }

    .delete-confirm {
        display: flex;
        align-items: center;

        .confirm-btn {
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-right: 4px;

            &:last-child {
                margin-right: 0;
            }

            &.confirm-yes {
                background: rgba($system-green, 0.1);
                color: $system-green;

                &:active {
                    transform: scale(0.92);
                    background: rgba($system-green, 0.2);
                }
            }

            &.confirm-no {
                background: rgba($system-red, 0.1);
                color: $system-red;

                &:active {
                    transform: scale(0.92);
                    background: rgba($system-red, 0.2);
                }
            }
        }
    }
}
</style>

<style lang="scss">
// 添加动画效果
@keyframes scoreHighlight {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.eval .score:not(.points .score) {
    animation: scoreHighlight 0.6s ease-out;
}

/* 添加按钮脉动动画 */
@keyframes buttonPulse {
    0% {
        box-shadow: 0 0 0 0 rgba(22, 119, 255, 0.4);
    }
    70% {
        box-shadow: 0 0 0 8px rgba(22, 119, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(22, 119, 255, 0);
    }
}
</style>
