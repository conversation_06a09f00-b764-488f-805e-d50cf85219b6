<template>
    <div class="recommend-courses">
        <div class="section-header">
            <h3 class="section-title">推荐课程</h3>
            <div class="more-link" @click="handleViewMore">
                更多
                <i class="pi pi-angle-right"></i>
            </div>
        </div>

        <div class="courses-list">
            <div
                v-for="course in courses"
                :key="course.id"
                class="course-card"
                @click="handleCourseClick(course)"
            >
                <!-- 左侧课程图片 -->
                <div class="course-image">
                    <div class="image-container" :class="course.type">
                        <img
                            :src="course.imageUrl"
                            :alt="course.name"
                            class="course-image-img"
                            @error="handleImageError"
                            @load="handleImageLoad"
                        />
                        <!-- 图片加载失败时的后备图标 -->
                        <i
                            v-if="course.imageLoadError"
                            :class="`pi ${course.icon}`"
                            class="course-icon fallback-icon"
                        ></i>
                    </div>
                </div>

                <!-- 右侧课程信息 -->
                <div class="course-content">
                    <div class="course-header">
                        <h4 class="course-name">{{ course.name }}</h4>
                        <div class="course-type-badge" :class="course.type">
                            <img
                                :src="getTypeBadgeImage(course.type)"
                                :alt="course.typeLabel"
                                class="badge-image"
                            />
                        </div>
                    </div>

                    <div class="course-info">
                        <div class="info-row">
                            <div class="info-item">
                                <i class="pi pi-building info-icon"></i>
                                <span class="value">{{ course.source }}</span>
                            </div>
                            <div class="info-item">
                                <i class="pi pi-book info-icon"></i>
                                <span class="value">{{ course.format }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 定义事件
const emit = defineEmits(['viewMore', 'courseClick']);

// Mock 课程数据 - 与 CourseFilter.vue 保持一致的数据结构
const courses = ref([
    {
        id: 1,
        name: '【2024版】汽车之家员工手册.pdf',
        source: '人力资源部',
        type: 'document',
        typeLabel: '课件',
        format: '课件（32页）',
        departmentId: 1,
        tagIds: [1],
        imageUrl:
            'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/smart-trainer/learn/coverurl.jpg',
        icon: 'pi-file-pdf',
        imageLoadError: false,
        targetUrl:
            'https://zhishi.autohome.com.cn/home/<USER>/file?targetId=9991127442514966478848'
    },
    {
        id: 2,
        name: '内审稽核系列.mp4',
        source: '内审部',
        type: 'video',
        typeLabel: '视频',
        format: '视频（10分钟）',
        departmentId: 1,
        tagIds: [2],
        imageUrl:
            'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/smart-trainer/learn/coverurl.jpg',
        icon: 'pi-play-circle',
        imageLoadError: false,
        targetUrl: 'https://example.com/course/internal-audit-series.mp4'
    },
    {
        id: 3,
        name: '售卖-2025Q3智慧助手招商方案.pdf',
        source: '网点',
        type: 'document',
        typeLabel: '课件',
        format: '课件（10页）',
        departmentId: 2,
        tagIds: [4],
        imageUrl:
            'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/smart-trainer/learn/coverurl.jpg',
        icon: 'pi-briefcase',
        imageLoadError: false,
        targetUrl: 'https://example.com/course/smart-assistant-investment-plan-2025q3.pdf'
    }
]);

// 类型图片映射
const typeImageMap = {
    document:
        'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/smart-trainer/main/ppt1.png',
    video: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/smart-trainer/main/video1.png'
};

/**
 * 根据课程类型获取对应的徽章图片
 * @param {string} type - 课程类型
 * @returns {string} 图片URL
 */
const getTypeBadgeImage = type => {
    return typeImageMap[type] || '/images/badge-default.png';
};

/**
 * 处理图片加载错误事件
 * @param {Event} event - 图片错误事件
 */
const handleImageError = event => {
    const img = event.target;
    const courseCard = img.closest('.course-card');
    if (courseCard) {
        const courseName = courseCard.querySelector('.course-name')?.textContent;
        const course = courses.value.find(c => c.name === courseName);
        if (course) {
            course.imageLoadError = true;
        }
    }
    console.warn('课程图片加载失败:', img.src);
};

/**
 * 处理图片加载成功事件
 * @param {Event} event - 图片加载事件
 */
const handleImageLoad = event => {
    const img = event.target;
    const courseCard = img.closest('.course-card');
    if (courseCard) {
        const courseName = courseCard.querySelector('.course-name')?.textContent;
        const course = courses.value.find(c => c.name === courseName);
        if (course) {
            course.imageLoadError = false;
        }
    }
};

/**
 * 处理课程点击事件
 * @param {Object} course - 课程对象
 */
const handleCourseClick = course => {
    console.log('点击课程:', course.name);
    emit('courseClick', course);
};

/**
 * 处理查看更多点击事件
 */
const handleViewMore = () => {
    console.log('查看更多课程');
    emit('viewMore');
};

// 暴露给父组件的方法
defineExpose({
    handleCourseClick,
    handleViewMore
});

// 生命周期
onMounted(() => {
    // 组件挂载后可以获取课程数据
    console.log('推荐课程组件已挂载');
});
</script>

<style lang="scss" scoped>
.recommend-courses {
    .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 6px;

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .more-link {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 14px;
            color: #666;
            cursor: pointer;
            transition: color 0.3s ease;

            &:hover {
                color: #1677ff;
            }

            i {
                font-size: 12px;
            }
        }
    }

    .courses-list {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }

    .course-card {
        background: white;
        border-radius: 12px;
        padding: 16px;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        border: 1px solid rgba(0, 0, 0, 0.04);
        display: flex;
        align-items: flex-start;
        gap: 12px;

        &:hover {
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
            border-color: rgba(22, 119, 255, 0.1);
            transform: translateY(-2px);
        }

        .course-image {
            flex-shrink: 0;

            .image-container {
                width: 50px;
                height: 50px;
                border-radius: 10px;
                display: flex;
                align-items: center;
                justify-content: center;
                position: relative;
                overflow: hidden;

                &.document {
                    background: linear-gradient(135deg, #fff7e6 0%, #ffecc7 100%);
                }

                &.video {
                    background: linear-gradient(135deg, #f6ffed 0%, #e6f7ff 100%);
                }

                .course-image-img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    border-radius: 8px;
                    transition: transform 0.2s ease;

                    &:hover {
                        transform: scale(1.05);
                    }
                }

                // 后备图标样式（图片加载失败时显示）
                .fallback-icon {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    font-size: 20px;
                    background: rgba(255, 255, 255, 0.9);
                    border-radius: 50%;
                    padding: 6px;

                    &.pi-file-pdf {
                        color: #fa8c16;
                    }

                    &.pi-play-circle {
                        color: #52c41a;
                    }

                    &.pi-briefcase {
                        color: #1677ff;
                    }
                }
            }
        }

        .course-content {
            flex: 1;
            min-width: 0;
            position: relative;
        }

        .course-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;

            .course-name {
                font-size: 14px;
                font-weight: 600;
                color: #000;
                margin: 0;
                flex: 1;
                margin-right: 40px;
                line-height: 1.4;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
            }

            .course-type-badge {
                position: absolute;
                top: 0;
                right: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1;

                .badge-image {
                    width: auto;
                    height: 20px;
                    max-width: 60px;
                    object-fit: contain;
                }
            }
        }

        .course-info {
            .info-row {
                display: flex;
                gap: 16px;
                flex-wrap: wrap;
            }

            .info-item {
                display: flex;
                align-items: center;
                gap: 4px;
                font-size: 12px;
                color: #595959;

                .info-icon {
                    font-size: 12px;
                    color: #8c8c8c;
                    flex-shrink: 0;
                }

                .value {
                    font-weight: 500;
                }
            }
        }
    }
}

// 响应式设计
@media (max-width: 480px) {
    .recommend-courses {
        .course-card {
            padding: 12px;
            gap: 10px;

            .course-image .image-container {
                width: 40px;
                height: 40px;
                border-radius: 8px;

                .course-image-img {
                    border-radius: 6px;
                }

                .fallback-icon {
                    font-size: 16px;
                    padding: 4px;
                }
            }

            .course-header {
                margin-bottom: 6px;

                .course-name {
                    font-size: 13px;
                    margin-right: 32px;
                }

                .course-type-badge .badge-image {
                    height: 20px;
                    max-width: 48px;
                }
            }

            .course-info {
                .info-row {
                    gap: 12px;
                }

                .info-item {
                    font-size: 11px;
                    gap: 3px;

                    .info-icon {
                        font-size: 11px;
                    }
                }
            }
        }
    }
}

@media (min-width: 768px) {
    .recommend-courses {
        .course-card {
            padding: 18px;
            gap: 14px;

            .course-image .image-container {
                width: 60px;
                height: 60px;

                .fallback-icon {
                    font-size: 22px;
                    padding: 8px;
                }
            }

            .course-header {
                margin-bottom: 10px;

                .course-name {
                    font-size: 15px;
                    margin-right: 45px;
                }

                .course-type-badge .badge-image {
                    height: 28px;
                    max-width: 68px;
                }
            }

            .course-info {
                .info-row {
                    gap: 18px;
                }

                .info-item {
                    font-size: 13px;

                    .info-icon {
                        font-size: 13px;
                    }
                }
            }
        }
    }
}
</style>
