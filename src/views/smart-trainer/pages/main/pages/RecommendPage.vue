<template>
    <div class="recommend-page">
        <!-- 轮播图区域 -->
        <div class="banner-section">
            <swiper
                :modules="[Autoplay, Pagination, EffectFade]"
                :slides-per-view="1"
                :space-between="0"
                :loop="true"
                :autoplay="{
                    delay: 3000,
                    disableOnInteraction: false
                }"
                :pagination="{ clickable: true }"
                :effect="'fade'"
                class="swiper-container"
            >
                <swiper-slide v-for="(banner, index) in banners" :key="index">
                    <img :src="banner.image" :alt="banner.title" class="banner-image" />
                    <div class="banner-content">
                        <h3 class="banner-title">{{ banner.title }}</h3>
                        <p class="banner-subtitle">{{ banner.subtitle }}</p>
                        <div class="banner-tag" v-if="banner.tag">
                            <span class="tag-text">{{ banner.tag }}</span>
                            <span class="tag-hot">🔥</span>
                        </div>
                    </div>
                </swiper-slide>
            </swiper>
        </div>

        <!-- 课程分类卡片 -->
        <CategorySection :categories="categories" @categoryClick="handleCategoryClick" />

        <!-- 推荐课程组件 -->
        <div class="recommend-courses-section">
            <RecommendCourses
                @viewMore="handleViewMore"
                @courseClick="handleCourseClick"
            />
        </div>
    </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue';
// 引入 Swiper
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Autoplay, Pagination, EffectFade } from 'swiper/modules';
import RecommendCourses from '../courese/Recommend.vue';
import CategorySection from '../courese/CategorySection.vue';

// 引入 Swiper 样式
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/effect-fade';

// 定义 props
const props = defineProps({
    banners: {
        type: Array,
        required: true
    },
    categories: {
        type: Array,
        required: true
    }
});

// 定义事件
const emit = defineEmits(['categoryClick', 'viewMore', 'courseClick']);

/**
 * 处理课程分类点击
 * @param {string} categoryKey - 分类键值
 */
const handleCategoryClick = categoryKey => {
    emit('categoryClick', categoryKey);
};

/**
 * 处理推荐课程查看更多点击事件
 */
const handleViewMore = () => {
    emit('viewMore');
};

/**
 * 处理推荐课程点击事件
 * @param {Object} course - 课程对象
 */
const handleCourseClick = course => {
    emit('courseClick', course);
};
</script>

<style lang="scss" scoped>
.recommend-page {
    padding: 10px 16px;
    flex: 1;
    overflow-y: auto;

    // 轮播图区域
    .banner-section {
        margin-bottom: 10px;

        .swiper-container {
            position: relative;
            border-radius: 6px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            // 动态高度，保持 500:150 宽高比 (10:3)
            aspect-ratio: 895 / 300;

            .swiper-slide {
                display: flex;
                align-items: center;
                justify-content: center;

                .banner-image {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }

                .banner-content {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    padding: 24px;
                    color: white;

                    .banner-title {
                        font-size: 16px;
                        font-weight: 600;
                        margin-bottom: 8px;
                    }

                    .banner-subtitle {
                        font-size: 14px;
                        opacity: 0.9;
                        margin-bottom: 16px;
                    }

                    .banner-tag {
                        display: flex;
                        align-items: center;
                        gap: 4px;
                        background: rgba(255, 255, 255, 0.2);
                        padding: 4px 12px;
                        border-radius: 16px;
                        width: fit-content;

                        .tag-text {
                            font-size: 12px;
                            font-weight: 500;
                        }
                    }
                }
            }

            // 覆盖 Swiper 分页器样式
            :deep(.swiper-pagination-bullet) {
                width: 8px;
                height: 8px;
                background: rgba(255, 255, 255, 0.7);
                opacity: 1;
                transition: all 0.3s ease;
            }

            :deep(.swiper-pagination-bullet-active) {
                width: 20px;
                border-radius: 4px;
                background: white;
            }
        }
    }
}
</style>
